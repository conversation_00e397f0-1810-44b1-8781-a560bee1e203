#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import PointCloud2
import sensor_msgs_py.point_cloud2 as pc2

class PointCloudVisualizationTest(Node):
    def __init__(self):
        super().__init__('pointcloud_test')
        self.subscription = self.create_subscription(
            PointCloud2,
            '/d435i_detection/pointcloud_raw',
            self.pointcloud_callback,
            10
        )
        self.get_logger().info('PointCloud visualization test started')

    def pointcloud_callback(self, msg):
        self.get_logger().info(f'Received pointcloud with {msg.width} points')
        self.get_logger().info(f'Frame: {msg.header.frame_id}')
        self.get_logger().info(f'Timestamp: {msg.header.stamp.sec}.{msg.header.stamp.nanosec}')
        
        # 检查点云数据
        points = list(pc2.read_points(msg, field_names=("x", "y", "z"), skip_nans=True))
        if points:
            self.get_logger().info(f'First few points: {points[:5]}')
            
            # 计算点云的范围
            x_vals = [p[0] for p in points[:1000]]  # 只检查前1000个点以避免性能问题
            y_vals = [p[1] for p in points[:1000]]
            z_vals = [p[2] for p in points[:1000]]
            
            if x_vals and y_vals and z_vals:
                self.get_logger().info(f'X range: {min(x_vals):.3f} to {max(x_vals):.3f}')
                self.get_logger().info(f'Y range: {min(y_vals):.3f} to {max(y_vals):.3f}')
                self.get_logger().info(f'Z range: {min(z_vals):.3f} to {max(z_vals):.3f}')
        else:
            self.get_logger().warn('No valid points found in pointcloud')

def main(args=None):
    rclpy.init(args=args)
    node = PointCloudVisualizationTest()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
