{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-1e2c1260bdcd24c42853.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "aliengo_description", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "aliengo_description_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-aliengo_description_uninstall-d50d12dac0afc3805b61.json", "name": "aliengo_description_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-0afe28666d5d1b6c725e.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ros2_ws/build/aliengo_description", "source": "/home/<USER>/ros2_ws/src/quadruped_ros2_control/descriptions/unitree/aliengo_description"}, "version": {"major": 2, "minor": 3}}