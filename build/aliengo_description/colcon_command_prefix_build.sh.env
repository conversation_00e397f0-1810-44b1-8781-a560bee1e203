AMENT_PREFIX_PATH=/home/<USER>/livox_ws/install/livox_ros_driver2:/opt/ros/humble
APPDIR=/tmp/.mount_cursore7ZEp7
APPIMAGE=/opt/cursor.appimage
ARGV0=/opt/cursor.appimage
CHROME_DESKTOP=cursor.desktop
CLUTTER_IM_MODULE=fcitx
CMAKE_PREFIX_PATH=/opt/openrobots:/opt/openrobots:/home/<USER>/livox_ws/install/livox_ros_driver2
COLCON=1
COLCON_PREFIX_PATH=/home/<USER>/livox_ws/install
COLORTERM=truecolor
CURSOR_TRACE_ID=3262ff8094c644af99bcd157d0ae11a9
DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus
DEFAULTS_PATH=/usr/share/gconf/ubuntu-xorg.default.path
DESKTOP_SESSION=ubuntu-xorg
DISPLAY=:1
GAZEBO_MASTER_URI=http://localhost:11345
GAZEBO_MODEL_DATABASE_URI=http://models.gazebosim.org
GAZEBO_MODEL_PATH=/usr/share/gazebo/../../share/gazebo-11/models:/usr/share/gazebo/../../share/gazebo-11/models::/opt/ros/humble/share/turtlebot3_gazebo/models:/opt/ros/humble/share/turtlebot3_gazebo/models
GAZEBO_PLUGIN_PATH=/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:
GAZEBO_RESOURCE_PATH=/usr/share/gazebo/../../share/gazebo-11:/usr/share/gazebo/../../share/gazebo-11:
GDK_BACKEND=x11
GDMSESSION=ubuntu-xorg
GIO_LAUNCHED_DESKTOP_FILE=/usr/share/applications/cursor.desktop
GIO_LAUNCHED_DESKTOP_FILE_PID=58584
GIT_ASKPASS=/tmp/.mount_cursore7ZEp7/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh
GJS_DEBUG_OUTPUT=stderr
GJS_DEBUG_TOPICS=JS ERROR;JS LOG
GNOME_DESKTOP_SESSION_ID=this-is-deprecated
GNOME_SHELL_SESSION_MODE=ubuntu
GPG_AGENT_INFO=/run/user/1000/gnupg/S.gpg-agent:0:1
GSETTINGS_SCHEMA_DIR=/tmp/.mount_cursore7ZEp7/usr/share/glib-2.0/schemas/:
GTK_IM_MODULE=fcitx
GTK_MODULES=gail:atk-bridge
HOME=/home/<USER>
IM_CONFIG_PHASE=1
INVOCATION_ID=50bce4a76f124690ad7f2327d8e3568a
JOURNAL_STREAM=8:13497
LANG=en_US.UTF-8
LANGUAGE=en
LC_ADDRESS=zh_CN.UTF-8
LC_IDENTIFICATION=zh_CN.UTF-8
LC_MEASUREMENT=zh_CN.UTF-8
LC_MONETARY=zh_CN.UTF-8
LC_NAME=zh_CN.UTF-8
LC_NUMERIC=zh_CN.UTF-8
LC_PAPER=zh_CN.UTF-8
LC_TELEPHONE=zh_CN.UTF-8
LC_TIME=zh_CN.UTF-8
LD_LIBRARY_PATH=/opt/openrobots/lib:/opt/openrobots/lib:/home/<USER>/livox_ws/install/livox_ros_driver2/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/share/gazebo/../../lib/x86_64-linux-gnu/gazebo-11/plugins:
LESSCLOSE=/usr/bin/lesspipe %s %s
LESSOPEN=| /usr/bin/lesspipe %s
LOGNAME=cg215
LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:
MANAGERPID=2528
MANDATORY_PATH=/usr/share/gconf/ubuntu-xorg.mandatory.path
OGRE_RESOURCE_PATH=/usr/lib/x86_64-linux-gnu/OGRE-1.9.0
OLDPWD=/home/<USER>/ros2_ws
ORIGINAL_XDG_CURRENT_DESKTOP=ubuntu:GNOME
OWD=/home/<USER>
PAPERSIZE=a4
PATH=/home/<USER>/.local/bin:/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin
PERLLIB=/tmp/.mount_cursore7ZEp7/usr/share/perl5/:/tmp/.mount_cursore7ZEp7/usr/lib/perl5/:
PKG_CONFIG_PATH=/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:
PWD=/home/<USER>/ros2_ws/build/aliengo_description
PYTHONPATH=/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages
QT_ACCESSIBILITY=1
QT_IM_MODULE=fcitx
QT_PLUGIN_PATH=/tmp/.mount_cursore7ZEp7/usr/lib/qt4/plugins/:/tmp/.mount_cursore7ZEp7/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_cursore7ZEp7/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_cursore7ZEp7/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_cursore7ZEp7/usr/lib32/qt4/plugins/:/tmp/.mount_cursore7ZEp7/usr/lib64/qt4/plugins/:/tmp/.mount_cursore7ZEp7/usr/lib/qt5/plugins/:/tmp/.mount_cursore7ZEp7/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_cursore7ZEp7/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_cursore7ZEp7/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_cursore7ZEp7/usr/lib32/qt5/plugins/:/tmp/.mount_cursore7ZEp7/usr/lib64/qt5/plugins/:
ROS_DISTRO=humble
ROS_DOMAIN_ID=43
ROS_LOCALHOST_ONLY=0
ROS_PYTHON_VERSION=3
ROS_VERSION=2
SESSION_MANAGER=local/cg215:@/tmp/.ICE-unix/2911,unix/cg215:/tmp/.ICE-unix/2911
SHELL=/bin/bash
SHLVL=1
SSH_AGENT_LAUNCHER=gnome-keyring
SSH_AUTH_SOCK=/run/user/1000/keyring/ssh
SYSTEMD_EXEC_PID=2975
TERM=xterm-256color
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=0.47.8
TURTLEBOT3_MODEL=waffle
USER=cg215
USERNAME=cg215
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=/tmp/.mount_cursore7ZEp7/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js
VSCODE_GIT_ASKPASS_NODE=/tmp/.mount_cursore7ZEp7/usr/share/cursor/cursor
VSCODE_GIT_IPC_HANDLE=/run/user/1000/vscode-git-cfe34cecdc.sock
WINDOWPATH=2
XAUTHORITY=/run/user/1000/gdm/Xauthority
XDG_CONFIG_DIRS=/etc/xdg/xdg-ubuntu-xorg:/etc/xdg
XDG_CURRENT_DESKTOP=Unity
XDG_DATA_DIRS=/tmp/.mount_cursore7ZEp7/usr/share/:/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop
XDG_MENU_PREFIX=gnome-
XDG_RUNTIME_DIR=/run/user/1000
XDG_SESSION_CLASS=user
XDG_SESSION_DESKTOP=ubuntu-xorg
XDG_SESSION_TYPE=x11
XMODIFIERS=@im=fcitx
_=/usr/bin/colcon
__GLX_VENDOR_LIBRARY_NAME=nvidia
__NV_PRIME_RENDER_OFFLOAD=1
