<?xml version="1.0" encoding="utf-8"  standalone="no"?>
<svg 
 width="600" height="480"
 viewBox="0 0 600 480"
 xmlns="http://www.w3.org/2000/svg"
 xmlns:xlink="http://www.w3.org/1999/xlink"
>

<title>Gnuplot</title>
<desc>Produced by GNUPLOT 5.2 patchlevel 2 </desc>

<g id="gnuplot_canvas">

<rect x="0" y="0" width="600" height="480" fill="none"/>
<defs>

	<circle id='gpDot' r='0.5' stroke-width='0.5'/>
	<path id='gpPt0' stroke-width='0.222' stroke='currentColor' d='M-1,0 h2 M0,-1 v2'/>
	<path id='gpPt1' stroke-width='0.222' stroke='currentColor' d='M-1,-1 L1,1 M1,-1 L-1,1'/>
	<path id='gpPt2' stroke-width='0.222' stroke='currentColor' d='M-1,0 L1,0 M0,-1 L0,1 M-1,-1 L1,1 M-1,1 L1,-1'/>
	<rect id='gpPt3' stroke-width='0.222' stroke='currentColor' x='-1' y='-1' width='2' height='2'/>
	<rect id='gpPt4' stroke-width='0.222' stroke='currentColor' fill='currentColor' x='-1' y='-1' width='2' height='2'/>
	<circle id='gpPt5' stroke-width='0.222' stroke='currentColor' cx='0' cy='0' r='1'/>
	<use xlink:href='#gpPt5' id='gpPt6' fill='currentColor' stroke='none'/>
	<path id='gpPt7' stroke-width='0.222' stroke='currentColor' d='M0,-1.33 L-1.33,0.67 L1.33,0.67 z'/>
	<use xlink:href='#gpPt7' id='gpPt8' fill='currentColor' stroke='none'/>
	<use xlink:href='#gpPt7' id='gpPt9' stroke='currentColor' transform='rotate(180)'/>
	<use xlink:href='#gpPt9' id='gpPt10' fill='currentColor' stroke='none'/>
	<use xlink:href='#gpPt3' id='gpPt11' stroke='currentColor' transform='rotate(45)'/>
	<use xlink:href='#gpPt11' id='gpPt12' fill='currentColor' stroke='none'/>
	<path id='gpPt13' stroke-width='0.222' stroke='currentColor' d='M0,1.330 L1.265,0.411 L0.782,-1.067 L-0.782,-1.076 L-1.265,0.411 z'/>
	<use xlink:href='#gpPt13' id='gpPt14' fill='currentColor' stroke='none'/>
	<filter id='textbox' filterUnits='objectBoundingBox' x='0' y='0' height='1' width='1'>
	  <feFlood flood-color='white' flood-opacity='1' result='bgnd'/>
	  <feComposite in='SourceGraphic' in2='bgnd' operator='atop'/>
	</filter>
	<filter id='greybox' filterUnits='objectBoundingBox' x='0' y='0' height='1' width='1'>
	  <feFlood flood-color='lightgrey' flood-opacity='1' result='grey'/>
	  <feComposite in='SourceGraphic' in2='grey' operator='atop'/>
	</filter>
</defs>
<g fill="none" color="white" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,422.4 L72.6,422.4 M558.7,422.4 L549.7,422.4  '/>	<g transform="translate(55.3,426.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 0</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,364.6 L72.6,364.6 M558.7,364.6 L549.7,364.6  '/>	<g transform="translate(55.3,368.5)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 5</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,306.9 L72.6,306.9 M558.7,306.9 L549.7,306.9  '/>	<g transform="translate(55.3,310.8)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 10</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,249.1 L72.6,249.1 M558.7,249.1 L549.7,249.1  '/>	<g transform="translate(55.3,253.0)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 15</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,191.4 L72.6,191.4 M558.7,191.4 L549.7,191.4  '/>	<g transform="translate(55.3,195.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 20</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,133.6 L72.6,133.6 M558.7,133.6 L549.7,133.6  '/>	<g transform="translate(55.3,137.5)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 25</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,75.9 L72.6,75.9 M558.7,75.9 L549.7,75.9  '/>	<g transform="translate(55.3,79.8)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 30</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,18.1 L72.6,18.1 M558.7,18.1 L549.7,18.1  '/>	<g transform="translate(55.3,22.0)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 35</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M144.8,422.4 L144.8,413.4 M144.8,18.1 L144.8,27.1  '/>	<g transform="translate(144.8,444.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >5,000,000</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M225.9,422.4 L225.9,413.4 M225.9,18.1 L225.9,27.1  '/>	<g transform="translate(225.9,444.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >10,000,000</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M307.1,422.4 L307.1,413.4 M307.1,18.1 L307.1,27.1  '/>	<g transform="translate(307.1,444.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >15,000,000</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M388.3,422.4 L388.3,413.4 M388.3,18.1 L388.3,27.1  '/>	<g transform="translate(388.3,444.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >20,000,000</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M469.4,422.4 L469.4,413.4 M469.4,18.1 L469.4,27.1  '/>	<g transform="translate(469.4,444.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >25,000,000</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M550.6,422.4 L550.6,413.4 M550.6,18.1 L550.6,27.1  '/>	<g transform="translate(550.6,444.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >30,000,000</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,18.1 L63.6,422.4 L558.7,422.4 L558.7,18.1 L63.6,18.1 Z  '/></g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<g transform="translate(16.3,220.3) rotate(270)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >Time (sec)</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<g transform="translate(311.1,471.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >#Darts</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
	<g id="gnuplot_plot_1" ><title>Reduce surface computation</title>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<g transform="translate(287.7,40.0)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" >Reduce surface computation</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<use xlink:href='#gpPt0' transform='translate(149.6,360.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(193.7,367.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.9,420.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(69.0,419.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.6,419.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.4,420.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(74.9,414.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(117.1,386.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.2,422.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(80.4,411.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.2,421.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.7,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.1,422.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(69.0,419.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.8,420.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.8,419.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.4,419.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.3,420.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(74.4,414.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(115.0,384.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.2,422.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(79.8,410.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.1,421.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.7,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.1,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.8,419.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(116.6,381.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(97.9,398.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(550.6,31.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(317.1,36.1) scale(4.50)' color='rgb(148,   0, 211)'/>
</g>
	</g>
	<g id="gnuplot_plot_2" ><title>Model Fit</title>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<g transform="translate(287.7,58.0)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" >Model Fit</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='rgb(  0, 158, 115)'  d='M296.0,54.1 L338.2,54.1 M65.9,422.4 L68.6,420.3 L73.6,416.4 L78.6,412.5 L83.6,408.6 L88.6,404.7
		L93.6,400.8 L98.6,396.9 L103.6,393.0 L108.6,389.1 L113.6,385.2 L118.6,381.3 L123.6,377.4 L128.6,373.5
		L133.6,369.6 L138.6,365.7 L143.6,361.8 L148.6,357.9 L153.6,354.0 L158.6,350.1 L163.6,346.2 L168.6,342.3
		L173.6,338.4 L178.6,334.5 L183.6,330.6 L188.6,326.7 L193.6,322.8 L198.6,318.9 L203.6,315.0 L208.6,311.1
		L213.6,307.2 L218.6,303.3 L223.6,299.4 L228.6,295.5 L233.6,291.6 L238.6,287.7 L243.6,283.8 L248.6,279.9
		L253.6,276.0 L258.6,272.1 L263.6,268.2 L268.6,264.3 L273.6,260.4 L278.6,256.5 L283.6,252.6 L288.6,248.7
		L293.6,244.8 L298.6,240.9 L303.6,237.0 L308.6,233.1 L313.7,229.2 L318.7,225.2 L323.7,221.3 L328.7,217.4
		L333.7,213.5 L338.7,209.6 L343.7,205.7 L348.7,201.8 L353.7,197.9 L358.7,194.0 L363.7,190.1 L368.7,186.2
		L373.7,182.3 L378.7,178.4 L383.7,174.5 L388.7,170.6 L393.7,166.7 L398.7,162.8 L403.7,158.9 L408.7,155.0
		L413.7,151.1 L418.7,147.2 L423.7,143.3 L428.7,139.4 L433.7,135.5 L438.7,131.6 L443.7,127.7 L448.7,123.8
		L453.7,119.9 L458.7,116.0 L463.7,112.1 L468.7,108.2 L473.7,104.3 L478.7,100.4 L483.7,96.5 L488.7,92.6
		L493.7,88.7 L498.7,84.8 L503.7,80.9 L508.7,77.0 L513.7,73.1 L518.7,69.2 L523.7,65.3 L528.7,61.4
		L533.7,57.5 L538.7,53.6 L543.7,49.7 L548.7,45.8 L553.7,41.9 L558.7,38.0  '/></g>
	</g>
<g fill="none" color="white" stroke="rgb(  0, 158, 115)" stroke-width="4.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="4.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
<g fill="none" color="black" stroke="black" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,18.1 L63.6,422.4 L558.7,422.4 L558.7,18.1 L63.6,18.1 Z  '/></g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
</g>
</svg>

