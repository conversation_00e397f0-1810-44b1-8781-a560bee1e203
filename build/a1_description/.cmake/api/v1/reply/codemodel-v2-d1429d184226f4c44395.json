{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-ded3172efc8b13cab98e.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "a1_description", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "a1_description_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-a1_description_uninstall-01be2298255a4db3bfd7.json", "name": "a1_description_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-205b28ae4470d6dbe924.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ros2_ws/build/a1_description", "source": "/home/<USER>/ros2_ws/src/quadruped_ros2_control/descriptions/unitree/a1_description"}, "version": {"major": 2, "minor": 3}}