[0.000000] (-) TimerEvent: {}
[0.000614] (-) JobUnselected: {'identifier': 'a1_description'}
[0.000668] (-) JobUnselected: {'identifier': 'aliengo_description'}
[0.000727] (-) JobUnselected: {'identifier': 'anymal_c_description'}
[0.000760] (-) JobUnselected: {'identifier': 'b2_description'}
[0.000779] (-) JobUnselected: {'identifier': 'blasfeo_colcon'}
[0.000797] (-) JobUnselected: {'identifier': 'camera_simulation'}
[0.000815] (-) JobUnselected: {'identifier': 'cgal5_colcon'}
[0.000832] (-) JobUnselected: {'identifier': 'control_input_msgs'}
[0.000850] (-) JobUnselected: {'identifier': 'controller_common'}
[0.000868] (-) JobUnselected: {'identifier': 'convex_plane_decomposition'}
[0.000991] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_msgs'}
[0.001040] (-) JobUnselected: {'identifier': 'convex_plane_decomposition_ros'}
[0.001094] (-) JobUnselected: {'identifier': 'cyberdog_description'}
[0.001140] (-) JobUnselected: {'identifier': 'elevation_map_converter'}
[0.001151] (-) JobUnselected: {'identifier': 'elevation_mapping'}
[0.001160] (-) JobUnselected: {'identifier': 'fast_lio'}
[0.001169] (-) JobUnselected: {'identifier': 'go1_description'}
[0.001177] (-) JobUnselected: {'identifier': 'go2_description'}
[0.001185] (-) JobUnselected: {'identifier': 'grid_map_filters_rsl'}
[0.001194] (-) JobUnselected: {'identifier': 'grid_map_sdf'}
[0.001205] (-) JobUnselected: {'identifier': 'gz_quadruped_hardware'}
[0.001217] (-) JobUnselected: {'identifier': 'gz_quadruped_playground'}
[0.001331] (-) JobUnselected: {'identifier': 'hardware_unitree_mujoco'}
[0.001369] (-) JobUnselected: {'identifier': 'hpipm_colcon'}
[0.001399] (-) JobUnselected: {'identifier': 'joystick_input'}
[0.001480] (-) JobUnselected: {'identifier': 'keyboard_input'}
[0.001507] (-) JobUnselected: {'identifier': 'kindr_msgs'}
[0.001533] (-) JobUnselected: {'identifier': 'kindr_ros'}
[0.001551] (-) JobUnselected: {'identifier': 'leg_pd_controller'}
[0.001568] (-) JobUnselected: {'identifier': 'lidar_simulation'}
[0.001584] (-) JobUnselected: {'identifier': 'lite3_description'}
[0.001599] (-) JobUnselected: {'identifier': 'livox_ros_driver2'}
[0.001615] (-) JobUnselected: {'identifier': 'ocs2_anymal_commands'}
[0.001631] (-) JobUnselected: {'identifier': 'ocs2_anymal_loopshaping_mpc'}
[0.001646] (-) JobUnselected: {'identifier': 'ocs2_anymal_models'}
[0.001661] (-) JobUnselected: {'identifier': 'ocs2_anymal_mpc'}
[0.001677] (-) JobUnselected: {'identifier': 'ocs2_ballbot'}
[0.001694] (-) JobUnselected: {'identifier': 'ocs2_ballbot_mpcnet'}
[0.001730] (-) JobUnselected: {'identifier': 'ocs2_ballbot_ros'}
[0.001751] (-) JobUnselected: {'identifier': 'ocs2_cartpole'}
[0.001766] (-) JobUnselected: {'identifier': 'ocs2_cartpole_ros'}
[0.001782] (-) JobUnselected: {'identifier': 'ocs2_centroidal_model'}
[0.001797] (-) JobUnselected: {'identifier': 'ocs2_core'}
[0.001812] (-) JobUnselected: {'identifier': 'ocs2_ddp'}
[0.001827] (-) JobUnselected: {'identifier': 'ocs2_double_integrator'}
[0.001842] (-) JobUnselected: {'identifier': 'ocs2_double_integrator_ros'}
[0.001858] (-) JobUnselected: {'identifier': 'ocs2_ipm'}
[0.001875] (-) JobUnselected: {'identifier': 'ocs2_legged_robot'}
[0.001891] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_mpcnet'}
[0.001908] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_raisim'}
[0.001923] (-) JobUnselected: {'identifier': 'ocs2_legged_robot_ros'}
[0.001940] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator'}
[0.001957] (-) JobUnselected: {'identifier': 'ocs2_mobile_manipulator_ros'}
[0.001973] (-) JobUnselected: {'identifier': 'ocs2_mpc'}
[0.001990] (-) JobUnselected: {'identifier': 'ocs2_mpcnet_core'}
[0.002018] (-) JobUnselected: {'identifier': 'ocs2_msgs'}
[0.002035] (-) JobUnselected: {'identifier': 'ocs2_oc'}
[0.002106] (-) JobUnselected: {'identifier': 'ocs2_pinocchio_interface'}
[0.002145] (-) JobUnselected: {'identifier': 'ocs2_python_interface'}
[0.002164] (-) JobUnselected: {'identifier': 'ocs2_qp_solver'}
[0.002184] (-) JobUnselected: {'identifier': 'ocs2_quadrotor'}
[0.002201] (-) JobUnselected: {'identifier': 'ocs2_quadrotor_ros'}
[0.002230] (-) JobUnselected: {'identifier': 'ocs2_quadruped_controller'}
[0.002257] (-) JobUnselected: {'identifier': 'ocs2_quadruped_interface'}
[0.002353] (-) JobUnselected: {'identifier': 'ocs2_quadruped_loopshaping_interface'}
[0.002379] (-) JobUnselected: {'identifier': 'ocs2_raisim_core'}
[0.002397] (-) JobUnselected: {'identifier': 'ocs2_robotic_assets'}
[0.002413] (-) JobUnselected: {'identifier': 'ocs2_robotic_tools'}
[0.002429] (-) JobUnselected: {'identifier': 'ocs2_ros_interfaces'}
[0.002445] (-) JobUnselected: {'identifier': 'ocs2_self_collision'}
[0.002460] (-) JobUnselected: {'identifier': 'ocs2_self_collision_visualization'}
[0.002475] (-) JobUnselected: {'identifier': 'ocs2_slp'}
[0.002491] (-) JobUnselected: {'identifier': 'ocs2_sphere_approximation'}
[0.002508] (-) JobUnselected: {'identifier': 'ocs2_sqp'}
[0.002526] (-) JobUnselected: {'identifier': 'ocs2_switched_model_interface'}
[0.002584] (-) JobUnselected: {'identifier': 'ocs2_switched_model_msgs'}
[0.002604] (-) JobUnselected: {'identifier': 'ocs2_thirdparty'}
[0.002634] (-) JobUnselected: {'identifier': 'pb_rm_simulation'}
[0.002719] (-) JobUnselected: {'identifier': 'qpOASES'}
[0.002757] (-) JobUnselected: {'identifier': 'qpoases_colcon'}
[0.002780] (-) JobUnselected: {'identifier': 'quadruped_integration_launch'}
[0.002801] (-) JobUnselected: {'identifier': 'realsense2_camera'}
[0.002821] (-) JobUnselected: {'identifier': 'realsense2_camera_msgs'}
[0.002840] (-) JobUnselected: {'identifier': 'realsense2_description'}
[0.002857] (-) JobUnselected: {'identifier': 'realsense_ros_gazebo'}
[0.002875] (-) JobUnselected: {'identifier': 'rl_quadruped_controller'}
[0.002892] (-) JobUnselected: {'identifier': 'robot_p'}
[0.002909] (-) JobUnselected: {'identifier': 'ros2_livox_simulation'}
[0.002926] (-) JobUnselected: {'identifier': 'segmented_planes_terrain_model'}
[0.002942] (-) JobUnselected: {'identifier': 'unitree_guide_controller'}
[0.002959] (-) JobUnselected: {'identifier': 'unitree_joystick_input'}
[0.002975] (-) JobUnselected: {'identifier': 'x30_description'}
[0.002998] (d435i_detection_ros2) JobQueued: {'identifier': 'd435i_detection_ros2', 'dependencies': OrderedDict()}
[0.003024] (d435i_detection_ros2) JobStarted: {'identifier': 'd435i_detection_ros2'}
[0.099521] (-) TimerEvent: {}
[0.199792] (-) TimerEvent: {}
[0.300097] (-) TimerEvent: {}
[0.400485] (-) TimerEvent: {}
[0.500797] (-) TimerEvent: {}
[0.601111] (-) TimerEvent: {}
[0.701431] (-) TimerEvent: {}
[0.750070] (d435i_detection_ros2) Command: {'cmd': ['/usr/bin/python3', '-W', 'ignore:setup.py install is deprecated', '-W', 'ignore:easy_install command is deprecated', 'setup.py', 'egg_info', '--egg-base', '../../build/d435i_detection_ros2', 'build', '--build-base', '/home/<USER>/ros2_ws/build/d435i_detection_ros2/build', 'install', '--record', '/home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log', '--single-version-externally-managed', 'install_data'], 'cwd': '/home/<USER>/ros2_ws/src/d435i_detection_ros2', 'env': {'GJS_DEBUG_TOPICS': 'JS ERROR;JS LOG', 'PYTHON_BASIC_REPL': '1', 'LESSOPEN': '| /usr/bin/lesspipe %s', 'GZ_SIM_RESOURCE_PATH': ':/home/<USER>/.gz/models:/home/<USER>/.gz/models', 'no_proxy': 'localhost,*********/8,::1,api.anthropic.com,*.anthropic.com', 'LANGUAGE': 'en', 'USER': 'cg215', 'LC_TIME': 'zh_CN.UTF-8', 'XDG_SESSION_TYPE': 'x11', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', '__GLX_VENDOR_LIBRARY_NAME': 'nvidia', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/ros2_ws/install/unitree_guide_controller/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface/lib:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model/lib:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller/lib:/home/<USER>/ros2_ws/install/qpoases_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_models/lib:/home/<USER>/ros2_ws/install/ocs2_anymal_commands/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_legged_robot/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros/lib:/home/<USER>/ros2_ws/install/ocs2_ballbot/lib:/home/<USER>/ros2_ws/install/ocs2_sqp/lib:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation/lib:/home/<USER>/ros2_ws/install/ocs2_slp/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization/lib:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator/lib:/home/<USER>/ros2_ws/install/ocs2_self_collision/lib:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces/lib:/home/<USER>/ros2_ws/install/ocs2_quadrotor/lib:/home/<USER>/ros2_ws/install/ocs2_double_integrator/lib:/home/<USER>/ros2_ws/install/ocs2_python_interface/lib:/home/<USER>/ros2_ws/install/ocs2_centroidal_model/lib:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface/lib:/home/<USER>/ros2_ws/install/ocs2_cartpole/lib:/home/<USER>/ros2_ws/install/ocs2_robotic_tools/lib:/home/<USER>/ros2_ws/install/ocs2_ipm/lib:/home/<USER>/ros2_ws/install/ocs2_ddp/lib:/home/<USER>/ros2_ws/install/hpipm_colcon/lib:/home/<USER>/ros2_ws/install/ocs2_qp_solver/lib:/home/<USER>/ros2_ws/install/ocs2_mpc/lib:/home/<USER>/ros2_ws/install/ocs2_oc/lib:/home/<USER>/ros2_ws/install/ocs2_core/lib:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/lib:/home/<USER>/ros2_ws/install/ocs2_msgs/lib:/home/<USER>/ros2_ws/install/fast_lio/lib:/home/<USER>/ros2_ws/install/livox_ros_driver2/lib:/home/<USER>/ros2_ws/install/leg_pd_controller/lib:/home/<USER>/ros2_ws/install/elevation_mapping/lib:/home/<USER>/ros2_ws/install/gz_quadruped_hardware/lib:/home/<USER>/ros2_ws/install/grid_map_sdf/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition/lib:/home/<USER>/ros2_ws/install/grid_map_filters_rsl/lib:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/lib:/home/<USER>/ros2_ws/install/controller_common/lib:/home/<USER>/ros2_ws/install/control_input_msgs/lib:/home/<USER>/ros2_ws/install/blasfeo_colcon/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/opt/openrobots/lib:', 'LESS': '-FX', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'OLDPWD': '/home/<USER>/ros2_ws', 'TERM_PROGRAM_VERSION': '1.101.2', 'DESKTOP_SESSION': 'ubuntu-xorg', 'NVM_BIN': '/home/<USER>/.nvm/versions/node/v22.16.0/bin', 'NVM_INC': '/home/<USER>/.nvm/versions/node/v22.16.0/include/node', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'zh_CN.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '2837', 'LC_CTYPE': 'zh_CN.UTF-8', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'SYSTEMD_EXEC_PID': '3419', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '19147', 'NVM_DIR': '/home/<USER>/.nvm', 'MANDATORY_PATH': '/usr/share/gconf/ubuntu-xorg.mandatory.path', 'COLCON_PREFIX_PATH': '/home/<USER>/ros2_ws/install', 'ROS_DISTRO': 'humble', 'GTK_IM_MODULE': 'fcitx', 'LOGNAME': 'cg215', 'JOURNAL_STREAM': '8:36906', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/opt/openrobots/lib/pkgconfig:/opt/openrobots/lib/pkgconfig:', 'CLAUDE_CODE_SSE_PORT': '38906', 'XDG_SESSION_CLASS': 'user', 'DEFAULTS_PATH': '/usr/share/gconf/ubuntu-xorg.default.path', 'USERNAME': 'cg215', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'ROS_LOCALHOST_ONLY': '0', 'WINDOWPATH': '2', 'PATH': '/home/<USER>/.nvm/versions/node/v22.16.0/bin:/home/<USER>/ros2_ws/install/d435i_yolo_ros/bin:/home/<USER>/ros2_ws/install/d435i_detetion/bin:/opt/openrobots/bin:/opt/openrobots/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/cg215:@/tmp/.ICE-unix/3400,unix/cg215:/tmp/.ICE-unix/3400', 'INVOCATION_ID': '4046170bdb2c4f09af14cb2d8fe2c218', 'PAPERSIZE': 'a4', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'zh_CN.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-7f29cd94f947265f.txt', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'LC_TELEPHONE': 'zh_CN.UTF-8', '__NV_PRIME_RENDER_OFFLOAD': '1', 'XMODIFIERS': '@im=fcitx', 'XDG_SESSION_DESKTOP': 'ubuntu-xorg', 'XAUTHORITY': '/run/user/1000/gdm/Xauthority', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-a172042c66.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AGENT_LAUNCHER': 'gnome-keyring', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/try_map:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/quadruped_integration_launch:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/fast_lio:/home/<USER>/ros2_ws/install/livox_ros_driver2:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/lidar_simulation:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/elevation_mapping:/home/<USER>/ros2_ws/install/kindr_ros:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/d435i_yolo_ros:/home/<USER>/ros2_ws/install/d435i_detetion:/home/<USER>/ros2_ws/install/d435i_detection_ros2:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/cgal5_catkin:/home/<USER>/ros2_ws/install/camera_simulation:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/ros/humble', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'LC_NAME': 'zh_CN.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu-xorg', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'LC_MEASUREMENT': 'zh_CN.UTF-8', 'GPG_AGENT_INFO': '/run/user/1000/gnupg/S.gpg-agent:0:1', 'GJS_DEBUG_OUTPUT': 'stderr', 'LC_IDENTIFICATION': 'zh_CN.UTF-8', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'QT_IM_MODULE': 'fcitx', 'PWD': '/home/<USER>/ros2_ws/build/d435i_detection_ros2', 'ENABLE_IDE_INTEGRATION': 'true', 'TURTLEBOT3_MODEL': 'waffle', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg', 'NVM_CD_FLAGS': '', 'XDG_DATA_DIRS': '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/ros2_ws/build/d435i_detection_ros2/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages:/home/<USER>/ros2_ws/install/try_map/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/ocs2_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/fast_lio/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/d435i_yolo_ros/lib/python3.10/site-packages:/home/<USER>/ros2_ws/install/d435i_detetion/lib/python3.10/site-packages:/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs/local/lib/python3.10/dist-packages:/home/<USER>/ros2_ws/install/control_input_msgs/local/lib/python3.10/dist-packages:/opt/openrobots/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages:/home/<USER>/livox_ws/install/livox_ros_driver2/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages', 'LC_NUMERIC': 'zh_CN.UTF-8', 'LC_PAPER': 'zh_CN.UTF-8', 'COLCON': '1', 'CMAKE_PREFIX_PATH': '/opt/openrobots:/home/<USER>/ros2_ws/install/x30_description:/home/<USER>/ros2_ws/install/unitree_guide_controller:/home/<USER>/ros2_ws/install/try_map:/home/<USER>/ros2_ws/install/ocs2_anymal_loopshaping_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_loopshaping_interface:/home/<USER>/ros2_ws/install/ocs2_anymal_mpc:/home/<USER>/ros2_ws/install/ocs2_quadruped_interface:/home/<USER>/ros2_ws/install/segmented_planes_terrain_model:/home/<USER>/ros2_ws/install/quadruped_integration_launch:/home/<USER>/ros2_ws/install/ocs2_quadruped_controller:/home/<USER>/ros2_ws/install/qpoases_colcon:/home/<USER>/ros2_ws/install/ocs2_anymal_models:/home/<USER>/ros2_ws/install/ocs2_anymal_commands:/home/<USER>/ros2_ws/install/ocs2_switched_model_interface:/home/<USER>/ros2_ws/install/ocs2_legged_robot_ros:/home/<USER>/ros2_ws/install/ocs2_legged_robot:/home/<USER>/ros2_ws/install/ocs2_ballbot_ros:/home/<USER>/ros2_ws/install/ocs2_ballbot:/home/<USER>/ros2_ws/install/ocs2_sqp:/home/<USER>/ros2_ws/install/ocs2_sphere_approximation:/home/<USER>/ros2_ws/install/ocs2_slp:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator_ros:/home/<USER>/ros2_ws/install/ocs2_self_collision_visualization:/home/<USER>/ros2_ws/install/ocs2_mobile_manipulator:/home/<USER>/ros2_ws/install/ocs2_self_collision:/home/<USER>/ros2_ws/install/ocs2_quadrotor_ros:/home/<USER>/ros2_ws/install/ocs2_double_integrator_ros:/home/<USER>/ros2_ws/install/ocs2_cartpole_ros:/home/<USER>/ros2_ws/install/ocs2_ros_interfaces:/home/<USER>/ros2_ws/install/ocs2_quadrotor:/home/<USER>/ros2_ws/install/ocs2_double_integrator:/home/<USER>/ros2_ws/install/ocs2_python_interface:/home/<USER>/ros2_ws/install/ocs2_centroidal_model:/home/<USER>/ros2_ws/install/ocs2_pinocchio_interface:/home/<USER>/ros2_ws/install/ocs2_cartpole:/home/<USER>/ros2_ws/install/ocs2_robotic_tools:/home/<USER>/ros2_ws/install/ocs2_ipm:/home/<USER>/ros2_ws/install/ocs2_ddp:/home/<USER>/ros2_ws/install/hpipm_colcon:/home/<USER>/ros2_ws/install/ocs2_qp_solver:/home/<USER>/ros2_ws/install/ocs2_mpc:/home/<USER>/ros2_ws/install/ocs2_oc:/home/<USER>/ros2_ws/install/ocs2_core:/home/<USER>/ros2_ws/install/ocs2_thirdparty:/home/<USER>/ros2_ws/install/ocs2_switched_model_msgs:/home/<USER>/ros2_ws/install/ocs2_robotic_assets:/home/<USER>/ros2_ws/install/ocs2_msgs:/home/<USER>/ros2_ws/install/fast_lio:/home/<USER>/ros2_ws/install/livox_ros_driver2:/home/<USER>/ros2_ws/install/lite3_description:/home/<USER>/ros2_ws/install/lidar_simulation:/home/<USER>/ros2_ws/install/leg_pd_controller:/home/<USER>/ros2_ws/install/elevation_mapping:/home/<USER>/ros2_ws/install/kindr_ros:/home/<USER>/ros2_ws/install/keyboard_input:/home/<USER>/ros2_ws/install/gz_quadruped_playground:/home/<USER>/ros2_ws/install/gz_quadruped_hardware:/home/<USER>/ros2_ws/install/grid_map_sdf:/home/<USER>/ros2_ws/install/convex_plane_decomposition_ros:/home/<USER>/ros2_ws/install/convex_plane_decomposition:/home/<USER>/ros2_ws/install/grid_map_filters_rsl:/home/<USER>/ros2_ws/install/go2_description:/home/<USER>/ros2_ws/install/go1_description:/home/<USER>/ros2_ws/install/elevation_map_converter:/home/<USER>/ros2_ws/install/cyberdog_description:/home/<USER>/ros2_ws/install/convex_plane_decomposition_msgs:/home/<USER>/ros2_ws/install/controller_common:/home/<USER>/ros2_ws/install/control_input_msgs:/home/<USER>/ros2_ws/install/cgal5_colcon:/home/<USER>/ros2_ws/install/cgal5_catkin:/home/<USER>/ros2_ws/install/camera_simulation:/home/<USER>/ros2_ws/install/blasfeo_colcon:/home/<USER>/ros2_ws/install/b2_description:/home/<USER>/ros2_ws/install/anymal_c_description:/home/<USER>/ros2_ws/install/aliengo_description:/home/<USER>/ros2_ws/install/a1_description:/opt/openrobots:'}, 'shell': False}
[0.801584] (-) TimerEvent: {}
[0.901963] (-) TimerEvent: {}
[0.916555] (d435i_detection_ros2) StderrLine: {'line': b"/home/<USER>/.local/lib/python3.10/site-packages/setuptools/_distutils/dist.py:289: UserWarning: Unknown distribution option: 'tests_require'\n"}
[0.916826] (d435i_detection_ros2) StderrLine: {'line': b'  warnings.warn(msg)\n'}
[1.002069] (-) TimerEvent: {}
[1.009314] (d435i_detection_ros2) StdoutLine: {'line': b'running egg_info\n'}
[1.022502] (d435i_detection_ros2) StdoutLine: {'line': b'writing ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/PKG-INFO\n'}
[1.023130] (d435i_detection_ros2) StdoutLine: {'line': b'writing dependency_links to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/dependency_links.txt\n'}
[1.023350] (d435i_detection_ros2) StdoutLine: {'line': b'writing entry points to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/entry_points.txt\n'}
[1.023562] (d435i_detection_ros2) StdoutLine: {'line': b'writing requirements to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/requires.txt\n'}
[1.023733] (d435i_detection_ros2) StdoutLine: {'line': b'writing top-level names to ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/top_level.txt\n'}
[1.050658] (d435i_detection_ros2) StdoutLine: {'line': b"reading manifest file '../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/SOURCES.txt'\n"}
[1.051920] (d435i_detection_ros2) StdoutLine: {'line': b"writing manifest file '../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info/SOURCES.txt'\n"}
[1.052066] (d435i_detection_ros2) StdoutLine: {'line': b'running build\n'}
[1.052176] (d435i_detection_ros2) StdoutLine: {'line': b'running build_py\n'}
[1.052314] (d435i_detection_ros2) StdoutLine: {'line': b'copying d435i_detection_ros2/sim_detection_node.py -> /home/<USER>/ros2_ws/build/d435i_detection_ros2/build/lib/d435i_detection_ros2\n'}
[1.052500] (d435i_detection_ros2) StdoutLine: {'line': b'copying d435i_detection_ros2/d435i_detection_node.py -> /home/<USER>/ros2_ws/build/d435i_detection_ros2/build/lib/d435i_detection_ros2\n'}
[1.052792] (d435i_detection_ros2) StdoutLine: {'line': b'running install\n'}
[1.055824] (d435i_detection_ros2) StdoutLine: {'line': b'running install_lib\n'}
[1.067488] (d435i_detection_ros2) StdoutLine: {'line': b'copying /home/<USER>/ros2_ws/build/d435i_detection_ros2/build/lib/d435i_detection_ros2/sim_detection_node.py -> /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2\n'}
[1.068166] (d435i_detection_ros2) StdoutLine: {'line': b'byte-compiling /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2/sim_detection_node.py to sim_detection_node.cpython-310.pyc\n'}
[1.071579] (d435i_detection_ros2) StdoutLine: {'line': b'running install_data\n'}
[1.072075] (d435i_detection_ros2) StdoutLine: {'line': b'running install_egg_info\n'}
[1.090549] (d435i_detection_ros2) StdoutLine: {'line': b"removing '/home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2-1.0.0-py3.10.egg-info' (and everything under it)\n"}
[1.090934] (d435i_detection_ros2) StdoutLine: {'line': b'Copying ../../build/d435i_detection_ros2/d435i_detection_ros2.egg-info to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/python3.10/site-packages/d435i_detection_ros2-1.0.0-py3.10.egg-info\n'}
[1.092401] (d435i_detection_ros2) StdoutLine: {'line': b'running install_scripts\n'}
[1.102175] (-) TimerEvent: {}
[1.130781] (d435i_detection_ros2) StdoutLine: {'line': b'Installing d435i_detection_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2\n'}
[1.130964] (d435i_detection_ros2) StdoutLine: {'line': b'Installing d435i_subscriber_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2\n'}
[1.131027] (d435i_detection_ros2) StdoutLine: {'line': b'Installing sim_detection_node script to /home/<USER>/ros2_ws/install/d435i_detection_ros2/lib/d435i_detection_ros2\n'}
[1.131486] (d435i_detection_ros2) StdoutLine: {'line': b"writing list of installed files to '/home/<USER>/ros2_ws/build/d435i_detection_ros2/install.log'\n"}
[1.155563] (d435i_detection_ros2) CommandEnded: {'returncode': 0}
[1.166437] (d435i_detection_ros2) JobEnded: {'identifier': 'd435i_detection_ros2', 'rc': 0}
[1.167234] (-) EventReactorShutdown: {}
