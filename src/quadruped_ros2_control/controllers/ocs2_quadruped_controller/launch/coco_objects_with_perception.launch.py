import os
import xacro
import tempfile
import yaml
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, OpaqueFunction, IncludeLaunchDescription, RegisterEventHandler, \
    TimerAction
from launch.event_handlers import OnProcessExit
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import PathJoinSubstitution, LaunchConfiguration
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
import launch.conditions


def launch_setup(context, *args, **kwargs):
    # 获取COCO世界路径
    coco_world_sdf_path = '/home/<USER>/ros2_ws/src/quadruped_ros2_control/libraries/gz_quadruped_playground/worlds/coco_objects_world.sdf'

    # 获取机器人初始位置参数
    init_x = context.launch_configurations['x_pos']
    init_y = context.launch_configurations['y_pos']
    init_height = context.launch_configurations['height']
    
    # 获取是否启用感知控制的参数
    enable_perceptive = context.launch_configurations['enable_perceptive']
    
    # 获取是否启用检测和分割的参数
    enable_detection = context.launch_configurations['enable_detection']
    
    # 获取是否启用分割高程图的参数
    enable_segmented_mapping = context.launch_configurations['enable_segmented_mapping']
    
    # 获取点云话题选择参数
    pointcloud_topic = context.launch_configurations['pointcloud_topic']
    
    gz_spawn_entity = Node(
        package='ros_gz_sim',
        executable='create',
        output='screen',
        arguments=['-topic', 'robot_description', '-name',
                   'robot', '-allow_renaming', 'true', 
                   '-x', init_x, '-y', init_y, '-z', init_height],
    )

    # Robot Description
    pkg_description = context.launch_configurations['pkg_description']
    pkg_path = os.path.join(get_package_share_directory(pkg_description))
    xacro_file = os.path.join(pkg_path, 'xacro', 'robot.xacro')
    robot_description = xacro.process_file(xacro_file, mappings={
        'GAZEBO': 'true'
    }).toxml()
    robot_state_publisher = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        parameters=[
            {
                'publish_frequency': 20.0,
                'use_tf_static': True,
                'robot_description': robot_description,
                'ignore_timestamp': True
            }
        ],
    )

    rviz_config_file = os.path.join(get_package_share_directory("ocs2_quadruped_controller"), "config", "visualize_ocs2.rviz")
    rviz = Node(
        package='rviz2',
        executable='rviz2',
        name='rviz',
        output='screen',
        arguments=["-d", rviz_config_file]
    )

    joint_state_publisher = Node(
        package="controller_manager",
        executable="spawner",
        arguments=["joint_state_broadcaster",
                   "--controller-manager", "/controller_manager"]
    )

    imu_sensor_broadcaster = Node(
        package="controller_manager",
        executable="spawner",
        arguments=["imu_sensor_broadcaster",
                   "--controller-manager", "/controller_manager"]
    )

    # OCS2控制器，启用感知功能
    ocs2_controller = Node(
        package="controller_manager",
        executable="spawner",
        arguments=["ocs2_quadruped_controller", "--controller-manager", "/controller_manager"],
        parameters=[{'enable_perceptive': enable_perceptive}]
    )

    # D435i检测和分割节点
    # 从RGB+depth生成原始点云和分割点云
    d435i_detection_node = Node(
        package='d435i_detection_ros2',
        executable='d435i_subscriber_node',
        name='d435i_detection_node',
        output='screen',
        parameters=[{
            'weights_path': 'yolov7-segmentation/weights/yolov7-seg.pt',
            'conf_threshold': 0.25,
            'iou_threshold': 0.45,
            'device': '',
            'enable_pointcloud': True,  # 必须启用才能工作
            'enable_segmented_pointcloud': True,  # 启用点云分割
            # 输入话题配置（d435i_subscriber_node支持的参数）
            'rgb_input_topic': '/rgbd/image',
            'depth_input_topic': '/rgbd/depth_image', 
            'camera_info_input_topic': '/rgbd/camera_info',
            # 输出话题配置（根据源码中的参数名）
            'pointcloud_raw_topic': '/d435i_detection/pointcloud_raw',
            'pointcloud_segmented_topic': '/d435i_detection/pointcloud_segmented',
            # 其他配置
            'use_stable_colors': True,
            'color_intensity': 1.0,
            'background_dimming': 0.3,
        }],
        condition=launch.conditions.IfCondition(enable_detection)
    )

    # 动态创建elevation_mapping配置
    def create_elevation_mapping_config(pointcloud_topic_name):
        """根据选择的点云话题动态创建elevation mapping配置"""
        
        # 基础配置
        base_config = {
            '/**': {
                'ros__parameters': {
                    'map_frame_id': "odom",
                    'robot_base_frame_id': "trunk", 
                    'robot_pose_with_covariance_topic': "/odom",
                    'track_point_frame_id': "trunk",
                    'track_point_x': 1.0,
                    'track_point_y': 0.0,
                    'track_point_z': 0.0
                }
            }
        }
        
        # 根据选择的话题配置输入源
        if pointcloud_topic_name == '/rgbd/points':
            base_config['/**']['ros__parameters']['inputs'] = ['d435i_pointcloud']
            base_config['/**']['ros__parameters']['d435i_pointcloud'] = {
                'type': 'pointcloud',
                'topic': '/rgbd/points',  # 直接使用Gazebo的原始点云，避免重建问题
                'queue_size': 100,
                'publish_on_update': True,
                'sensor_processor': {
                    'type': 'structured_light'
                }
            }
        elif pointcloud_topic_name == '/livox/points':
            base_config['/**']['ros__parameters']['inputs'] = ['livox_pointcloud']
            base_config['/**']['ros__parameters']['livox_pointcloud'] = {
                'type': 'pointcloud',
                'topic': '/livox/points',
                'queue_size': 100,
                'publish_on_update': True,
                'sensor_processor': {
                    'type': 'laser',
                    'min_radius': 0.001745329,
                    'beam_angle': 0.0014,
                    'beam_constant': 0.0015
                }
            }
        elif pointcloud_topic_name == 'both':
            # 使用两个点云源
            base_config['/**']['ros__parameters']['inputs'] = ['d435i_pointcloud', 'livox_pointcloud']
            base_config['/**']['ros__parameters']['d435i_pointcloud'] = {
                'type': 'pointcloud',
                'topic': '/rgbd/points',  # 直接使用Gazebo的原始点云，避免重建问题
                'queue_size': 100,
                'publish_on_update': True,
                'sensor_processor': {
                    'type': 'structured_light'
                }
            }
            base_config['/**']['ros__parameters']['livox_pointcloud'] = {
                'type': 'pointcloud',
                'topic': '/livox/points',
                'queue_size': 100,
                'publish_on_update': True,
                'sensor_processor': {
                    'type': 'laser',
                    'min_radius': 0.001745329,
                    'beam_angle': 0.0014,
                    'beam_constant': 0.0015
                }
            }
        
        return base_config
    
    # 创建分割高程图配置 - 参考实物实现
    def create_segmented_elevation_mapping_config():
        """创建分割高程图配置，使用分割点云数据，包含color层"""
        
        segmented_config = {
            '/**': {
                'ros__parameters': {
                    'map_frame_id': "odom",
                    'robot_base_frame_id': "trunk", 
                    'robot_pose_with_covariance_topic': "/odom",
                    'track_point_frame_id': "trunk",
                    'track_point_x': 1.0,
                    'track_point_y': 0.0,
                    'track_point_z': 0.0,
                    'inputs': ['d435i_segmented_pointcloud'],
                    'd435i_segmented_pointcloud': {
                        'type': 'pointcloud',
                        'topic': '/d435i_detection/pointcloud_segmented',
                        'queue_size': 100,
                        'publish_on_update': True,
                        'sensor_processor': {
                            'type': 'perfect',  # 使用perfect类型，支持颜色
                            'ignore_points_above': 5.0,
                            'ignore_points_below': -1.0,
                        }
                    },
                    # 启用分割相关的层
                    'additional_layers': [
                        'segmentation_id',
                        'segmentation_color_r', 
                        'segmentation_color_g',
                        'segmentation_color_b'
                    ]
                }
            }
        }
        
        return segmented_config
    
    # 创建临时配置文件
    elevation_config = create_elevation_mapping_config(pointcloud_topic)
    
    # 创建临时文件来存储动态配置
    temp_config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
    yaml.dump(elevation_config, temp_config_file, default_flow_style=False)
    temp_config_file.close()
    
    # 加载elevation_mapping节点
    share_dir = get_package_share_directory('elevation_mapping')
    config_dir = os.path.join(share_dir, 'config')
    list_params = []
    
    # 添加动态创建的机器人配置文件
    list_params.append(temp_config_file.name)
    
    # 添加其他固定配置文件
    for filee in ["elevation_maps/long_range.yaml","sensor_processors/realsense_d435.yaml","postprocessing/postprocessor_pipeline.yaml"]:
        list_params.append(os.path.join(config_dir, filee))
    
    elevation_mapping_node = Node(
        package='elevation_mapping',
        executable='elevation_mapping',
        name='elevation_mapping',
        output='screen',
        parameters=list_params + [{'use_sim_time': True}],
    )
    
    # 分割高程图节点配置
    segmented_elevation_config = create_segmented_elevation_mapping_config()
    temp_segmented_config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
    yaml.dump(segmented_elevation_config, temp_segmented_config_file, default_flow_style=False)
    temp_segmented_config_file.close()
    
    # 分割高程图参数列表
    segmented_list_params = [temp_segmented_config_file.name]
    for filee in ["elevation_maps/long_range.yaml", "sensor_processors/realsense_d435.yaml", "postprocessing/postprocessor_pipeline.yaml"]:
        segmented_list_params.append(os.path.join(config_dir, filee))
    
    # 分割高程图节点
    segmented_elevation_mapping_node = Node(
        package='elevation_mapping',
        executable='elevation_mapping',
        name='segmented_elevation_mapping',
        output='screen',
        parameters=segmented_list_params + [{'use_sim_time': True}],
        condition=launch.conditions.IfCondition(enable_segmented_mapping),
        remappings=[
            ('/elevation_map', '/segmented_elevation_map'),
            ('/elevation_map_raw', '/segmented_elevation_map_raw')
        ]
    )
    
    # 获取平面分解配置文件路径
    convex_plane_config_file = os.path.join(
        get_package_share_directory('ocs2_quadruped_controller'), 
        'config', 
        'convex_plane_decomposition_node.yaml'
    )
    
    # 添加convex_plane_decomposition节点，将elevation_map转换为平面区域
    # 使用配置文件代替硬编码参数
    convex_plane_decomp_node = Node(
        package='convex_plane_decomposition_ros',
        executable='convex_plane_decomposition_ros_node',
        name='convex_plane_decomposition',
        output='screen',
        parameters=[convex_plane_config_file, {'use_sim_time': True}],
        condition=launch.conditions.IfCondition(enable_perceptive)
    )
    
    keyboard_input_node = Node(
        package='keyboard_input',
        executable='keyboard_input',
        name='keyboard_input_node',
        output='screen'
    )
    
    launch_list = [
        rviz,
        robot_state_publisher,
        gz_spawn_entity,
        keyboard_input_node,
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                [PathJoinSubstitution([FindPackageShare('ros_gz_sim'),
                                       'launch',
                                       'gz_sim.launch.py'])]),
            launch_arguments=[('gz_args', [' -r -v 4 ', coco_world_sdf_path])]),
        RegisterEventHandler(
            event_handler=OnProcessExit(
                target_action=gz_spawn_entity,
                on_exit=[joint_state_publisher, imu_sensor_broadcaster],
            )
        ),
        RegisterEventHandler(
            event_handler=OnProcessExit(
                target_action=joint_state_publisher,
                on_exit=[ocs2_controller],
            )
        ),
    ]
    
    # 检测节点启动
    if enable_detection.lower() == 'true':
        launch_list.append(
            RegisterEventHandler(
                event_handler=OnProcessExit(
                    target_action=ocs2_controller,
                    on_exit=[d435i_detection_node],
                )
            )
        )
    
    # 仅在启用感知功能时添加相关节点
    if enable_perceptive.lower() == 'true':
        if enable_detection.lower() == 'true':
            # 如果启用检测，elevation mapping在检测节点之后启动
            launch_list.append(
                RegisterEventHandler(
                    event_handler=OnProcessExit(
                        target_action=d435i_detection_node,
                        on_exit=[elevation_mapping_node],
                    )
                )
            )
        else:
            # 如果未启用检测，直接在controller之后启动
            launch_list.append(
                RegisterEventHandler(
                    event_handler=OnProcessExit(
                        target_action=ocs2_controller,
                        on_exit=[elevation_mapping_node],
                    )
                )
            )
        
        # 分割高程图节点启动
        if enable_segmented_mapping.lower() == 'true' and enable_detection.lower() == 'true':
            launch_list.append(
                RegisterEventHandler(
                    event_handler=OnProcessExit(
                        target_action=elevation_mapping_node,
                        on_exit=[segmented_elevation_mapping_node],
                    )
                )
            )
            # convex plane decomposition在分割高程图之后启动
            launch_list.append(
                RegisterEventHandler(
                    event_handler=OnProcessExit(
                        target_action=segmented_elevation_mapping_node,
                        on_exit=[convex_plane_decomp_node],
                    )
                )
            )
        else:
            # 如果没有分割高程图，convex plane decomposition在标准高程图之后启动
            launch_list.append(
                RegisterEventHandler(
                    event_handler=OnProcessExit(
                        target_action=elevation_mapping_node,
                        on_exit=[convex_plane_decomp_node],
                    )
                )
            )
    
    return launch_list


def generate_launch_description():
    pkg_description = DeclareLaunchArgument(
        'pkg_description',
        default_value='go2_description',
        description='package for robot description'
    )

    # 添加机器人初始位置参数，默认在开阔地面上
    x_pos = DeclareLaunchArgument(
        'x_pos',
        default_value='-6.0',
        description='Initial x position of the robot'
    )

    y_pos = DeclareLaunchArgument(
        'y_pos',
        default_value='0.0',
        description='Initial y position of the robot'
    )

    height = DeclareLaunchArgument(
        'height',
        default_value='0.3',
        description='Initial height (z) of the robot in simulation'
    )
    
    # 添加启用感知控制的参数
    enable_perceptive = DeclareLaunchArgument(
        'enable_perceptive',
        default_value='true',
        description='Enable perceptive controller functionality'
    )
    
    # 添加点云话题选择参数
    pointcloud_topic = DeclareLaunchArgument(
        'pointcloud_topic',
        default_value='/rgbd/points',
        description='Choose pointcloud topic for elevation mapping: /rgbd/points, /livox/points, or both'
    )

    # 添加启用检测和分割的参数
    enable_detection = DeclareLaunchArgument(
        'enable_detection',
        default_value='true',
        description='Enable D435i detection and segmentation functionality'
    )
    
    # 添加启用分割高程图的参数
    enable_segmented_mapping = DeclareLaunchArgument(
        'enable_segmented_mapping',
        default_value='true',
        description='Enable segmented elevation mapping with color layers'
    )

    # 创建传感器数据桥接节点
    gz_bridge_node = Node(
        package='ros_gz_bridge',
        executable='parameter_bridge',
        arguments=["/clock@rosgraph_msgs/msg/Clock[gz.msgs.Clock",
                   "/livox/points@sensor_msgs/msg/<EMAIL>",
                   '/livox/imu@sensor_msgs/msg/<EMAIL>',
                   '/color/image_raw@sensor_msgs/msg/<EMAIL>',
                   '/infra1/image_raw@sensor_msgs/msg/<EMAIL>',
                   '/infra2/image_raw@sensor_msgs/msg/<EMAIL>',
                   '/rgbd/image@sensor_msgs/msg/<EMAIL>',
                   '/rgbd/depth_image@sensor_msgs/msg/<EMAIL>',
                   '/rgbd/points@sensor_msgs/msg/<EMAIL>',
                   '/rgbd/camera_info@sensor_msgs/msg/<EMAIL>',  # 添加相机信息话题桥接
                   '/camera/imu@sensor_msgs/msg/<EMAIL>'],
        output='screen',
        parameters=[
            {'use_sim_time': True},
        ]
    )

    # TF静态转换节点，确保坐标系统一致
    tf_static_node = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='tf_elevation_to_odom',
        arguments=['0', '0', '0', '0', '0', '0', 'odom', 'elevation_mapping'],
        output='screen'
    )
    
    # 为D435i相机添加TF变换 - 修复坐标系名称
    tf_camera_optical_node = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='tf_camera_optical_frame',
        arguments=['0', '0', '0', '0', '0', '0', 'd435i_color_optical_frame', 'camera_color_optical_frame'],
        output='screen',
        condition=launch.conditions.IfCondition(LaunchConfiguration('enable_detection'))
    )

    return LaunchDescription([
        pkg_description,
        x_pos,
        y_pos,
        height,
        enable_perceptive,
        pointcloud_topic,
        enable_detection,
        enable_segmented_mapping,
        gz_bridge_node,
        tf_static_node,
        tf_camera_optical_node,
        OpaqueFunction(function=launch_setup),
    ])